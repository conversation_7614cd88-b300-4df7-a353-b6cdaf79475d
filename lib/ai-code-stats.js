#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const https = require('https');

// 配置项
const CONFIG = {
  // 远程API配置
  api: {
    url: 'https://connector.dingtalk.com/webhook/flow/1032e1dd14f40bbb57510005',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-info': 'pushcode'
    }
  },
  // 是否在控制台输出详细信息
  verbose: false,
  // 是否在推送时发送统计数据
  sendOnPush: true,
  // 是否在统计完成后清理AI标记
  cleanupMarkers: false
};

// 获取命令行参数
const commitHash = process.argv[2] || 'HEAD';
const outputMode = process.argv[3] || 'summary'; // 'detailed' 或 'summary'
const cleanupMode = process.argv[4] || 'auto'; // 'auto', 'no-cleanup'

// 生成 commit review 链接
function genCommitLink(project) {
  return `https://code.alibaba-inc.com/${project}/commit/${commitHash}`;
}

/**
 * 获取仓库信息
 */
function getRepoInfo() {
  const repoUrl = execSync('git config --get remote.origin.url', { encoding: 'utf-8' }).trim();
  const branchName = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf-8' }).trim();
  let projectName = repoUrl.split(':')[1].replace('.git', '');

  if (repoUrl.startsWith('http')) {
    projectName =  repoUrl.replace('https://code.alibaba-inc.com/', '').replace('.git', '');
  }
  
  return { repoUrl, projectName, branchName, commitLink: genCommitLink(projectName) };
}

/**
 * 获取提交信息
 */
function getCommitInfo(hash) {
  const author = execSync(`git show -s --format="%an <%ae>" ${hash}`, { encoding: 'utf-8' }).trim();
  const message = execSync(`git show -s --format="%B" ${hash}`, { encoding: 'utf-8' }).trim();
  const timestamp = execSync(`git show -s --format="%at" ${hash}`, { encoding: 'utf-8' }).trim();
  const date = new Date(parseInt(timestamp) * 1000).toISOString();
  
  return { hash, author, message, date };
}

/**
 * 获取提交的代码统计信息
 */
function getCodeStats(hash) {
  try {
    // 获取总体变更统计
    const diffCommand = `git diff --numstat ${hash}^..${hash}`;
    const diffOutput = execSync(diffCommand, { encoding: 'utf-8' }).trim();
    
    if (!diffOutput) {
      return { 
        files: 0,
        totalAdded: 0,
        totalDeleted: 0,
        netChange: 0,
        details: []
      };
    }
    
    const stats = diffOutput
      .split('\n')
      .filter(Boolean)
      .map(line => {
        const [added, deleted, file] = line.split(/\s+/);
        return { 
          file, 
          added: isNaN(parseInt(added)) ? 0 : parseInt(added), 
          deleted: isNaN(parseInt(deleted)) ? 0 : parseInt(deleted) 
        };
      });
    
    let totalAdded = 0;
    let totalDeleted = 0;
    
    stats.forEach(stat => {
      totalAdded += stat.added;
      totalDeleted += stat.deleted;
    });
    
    return { 
      files: stats.length,
      totalAdded,
      totalDeleted,
      netChange: totalAdded - totalDeleted,
      details: stats
    };
  } catch (error) {
    console.error(`获取代码统计出错: ${error.message}`);
    // 如果是首次提交，没有父提交可比较
    if (error.message.includes('fatal: bad revision')) {
      try {
        // 对于首次提交，统计所有添加的行
        const filesOutput = execSync(`git ls-tree -r ${hash} --name-only`, { encoding: 'utf-8' }).trim();
        const files = filesOutput.split('\n').filter(Boolean);
        
        let totalAdded = 0;
        const details = [];
        
        for (const file of files) {
          try {
            const content = execSync(`git show ${hash}:${file}`, { encoding: 'utf-8' });
            const lines = content.split('\n').length;
            details.push({ file, added: lines, deleted: 0 });
            totalAdded += lines;
          } catch (e) {
            // 忽略二进制文件等无法处理的文件
          }
        }
        
        return {
          files: files.length,
          totalAdded,
          totalDeleted: 0,
          netChange: totalAdded,
          details
        };
      } catch (e) {
        console.error(`处理首次提交统计出错: ${e.message}`);
      }
    }
    
    return { 
      files: 0,
      totalAdded: 0,
      totalDeleted: 0,
      netChange: 0,
      details: []
    };
  }
}

/**
 * 统计AI生成的代码
 */
function getAICodeStats(hash) {
  // 获取指定提交中修改的文件列表
  let changedFiles = [];
  try {
    changedFiles = execSync(`git diff-tree --no-commit-id --name-only -r ${hash}`, { encoding: 'utf-8' })
      .trim()
      .split('\n')
      .filter(Boolean);
  } catch (error) {
    console.error(`获取修改文件列表出错: ${error.message}`);
    return {
      filesWithAICode: 0,
      totalAILines: 0,
      details: []
    };
  }
  
  let totalAILines = 0;
  let filesWithAICode = 0;
  const aiCodeDetails = [];

  // 检查每个文件中的AI生成代码
  changedFiles.forEach(filePath => {
    try {
      // 获取该提交时的文件内容
      const fileContent = execSync(`git show ${hash}:${filePath}`, { encoding: 'utf-8' });
      
      // 计算AI生成的代码行数
      const aiCodeBlocks = [];
      let isInsideAIBlock = false;
      let currentBlock = [];
      let aiLinesInFile = 0;

      const lines = fileContent.split('\n');
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (line.includes('[[GBAI STAR]]')) {
          isInsideAIBlock = true;
          currentBlock = [];
        } else if (line.includes('[[GBAI END]]')) {
          isInsideAIBlock = false;
          aiCodeBlocks.push(currentBlock);
          aiLinesInFile += currentBlock.length;
        } else if (isInsideAIBlock) {
          currentBlock.push(line);
        }
      }

      if (aiLinesInFile > 0) {
        aiCodeDetails.push({ file: filePath, aiLines: aiLinesInFile });
        totalAILines += aiLinesInFile;
        filesWithAICode++;
      }
    } catch (error) {
      if (CONFIG.verbose) {
        console.error(`无法处理文件 ${filePath}: ${error.message}`);
      }
    }
  });

  return {
    filesWithAICode,
    totalAILines,
    details: aiCodeDetails
  };
}

/**
 * 清理工作目录中的AI标记
 */
function cleanupAIMarkers() {
  try {
    // 获取当前工作目录中的所有文件（排除.git目录）
    const findCommand = `find . -type f -not -path "./.git/*" -not -path "./node_modules/*" -not -name "*.log" -not -name "*.tmp"`;
    const allFiles = execSync(findCommand, { encoding: 'utf-8' })
      .trim()
      .split('\n')
      .filter(Boolean);

    let cleanedFiles = 0;
    let totalMarkersRemoved = 0;

    allFiles.forEach(filePath => {
      try {
        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf-8');

        // 检查是否包含AI标记
        if (content.includes('[[GBAI STAR]]') || content.includes('[[GBAI END]]')) {
          // 清理AI标记
          let cleanedContent = content;
          let markersInFile = 0;

          // 计算并移除标记
          const starMatches = content.match(/.*\[\[GBAI STAR\]\].*/g) || [];
          const endMatches = content.match(/.*\[\[GBAI END\]\].*/g) || [];
          markersInFile = starMatches.length + endMatches.length;

          // 移除包含标记的整行
          cleanedContent = cleanedContent
            .split('\n')
            .filter(line => !line.includes('[[GBAI STAR]]') && !line.includes('[[GBAI END]]'))
            .join('\n');

          // 写回文件
          fs.writeFileSync(filePath, cleanedContent, 'utf-8');

          if (markersInFile > 0) {
            cleanedFiles++;
            totalMarkersRemoved += markersInFile;
            if (CONFIG.verbose) {
              console.log(`  清理文件: ${filePath} (移除 ${markersInFile} 个标记)`);
            }
          }
        }
      } catch (error) {
        // 忽略无法处理的文件（如二进制文件）
        if (CONFIG.verbose && !error.message.includes('EISDIR')) {
          console.error(`  跳过文件 ${filePath}: ${error.message}`);
        }
      }
    });

    return { cleanedFiles, totalMarkersRemoved };
  } catch (error) {
    console.error(`清理AI标记时出错: ${error.message}`);
    return { cleanedFiles: 0, totalMarkersRemoved: 0 };
  }
}

/**
 * 向远程API发送统计数据
 */
function sendStatsToAPI(data) {
  return new Promise((resolve, reject) => {
    const apiUrl = new URL(CONFIG.api.url);
    const requestOptions = {
      hostname: apiUrl.hostname,
      port: apiUrl.port || (apiUrl.protocol === 'https:' ? 443 : 80),
      path: apiUrl.pathname + apiUrl.search,
      method: CONFIG.api.method,
      headers: CONFIG.api.headers
    };

    const req = https.request(requestOptions, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve({ statusCode: res.statusCode, data: responseData });
        } else {
          reject(new Error(`API请求失败: ${res.statusCode} ${responseData}`));
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    req.write(JSON.stringify(data));
    req.end();
  });
}

/**
 * 主函数
 */
async function main() {
  try {
    // 收集所有统计信息
    const repoInfo = getRepoInfo();
    const commitInfo = getCommitInfo(commitHash);
    const codeStats = getCodeStats(commitHash);
    const aiCodeStats = getAICodeStats(commitHash);
    
    // 组合所有统计数据
    const statsData = {
      repository: repoInfo,
      commit: commitInfo,
      codeChanges: codeStats,
      aiCode: aiCodeStats,
      timestamp: new Date().toISOString(),
      event: 'pushcode' // 标记这是push事件触发的统计
    };
    
    // 输出统计信息到控制台
    if (aiCodeStats.totalAILines > 0) {
      const aiPercentage = codeStats.totalAdded > 0 ? (aiCodeStats.totalAILines / codeStats.totalAdded * 100).toFixed(1) : '0';
      console.log(`  AI代码: ${aiCodeStats.totalAILines}行 (${aiPercentage}%) 在${aiCodeStats.filesWithAICode}个文件中`);
    } else {
      console.log(`  AI代码: 无`);
    }

    if (CONFIG.verbose) {
      if (outputMode === 'summary') {
        // 简洁模式：只输出关键信息
        console.log(`[${commitInfo.hash.substring(0, 8)}] ${commitInfo.message.split('\n')[0]}`);
        // console.log(`  代码变更: +${codeStats.totalAdded}/-${codeStats.totalDeleted} (${codeStats.files}个文件)`);
      } else {
        // 详细模式：完整的统计报告
        console.log('\n===== 代码提交统计 =====');
        console.log(`分支: ${repoInfo.branchName}`);
        console.log(`项目: ${repoInfo.projectName}`);
        console.log(`提交: ${commitInfo.hash.substring(0, 8)}`);
        console.log(`作者: ${commitInfo.author}`);
        console.log(`时间: ${commitInfo.date}`);
        console.log(`消息: ${commitInfo.message}`);
        console.log('\n代码变更:');
        console.log(`- 修改文件数: ${codeStats.files}`);
        console.log(`- 添加行数: ${codeStats.totalAdded}`);
        console.log(`- 删除行数: ${codeStats.totalDeleted}`);
        console.log(`- 净变更: ${codeStats.netChange}`);
        console.log('\nAI生成代码:');
        console.log(`- 包含AI代码的文件数: ${aiCodeStats.filesWithAICode}`);
        console.log(`- AI生成的总代码行数: ${aiCodeStats.totalAILines}`);

        // 避免除以零
        if (codeStats.totalAdded > 0) {
          console.log(`- AI代码占比: ${(aiCodeStats.totalAILines / codeStats.totalAdded * 100).toFixed(2)}%`);
        }
      }
    }
    
    // 发送统计数据到远程API
    if (CONFIG.sendOnPush) {
      try {
        const response = await sendStatsToAPI(statsData);
        if (CONFIG.verbose) {
          console.log('\n统计数据已成功发送到远程API');
        }
      } catch (apiError) {
        console.error('\n发送统计数据失败:', apiError.message);
      }
    }

    // 清理AI标记（根据cleanupMode参数决定）
    if (CONFIG.cleanupMarkers && cleanupMode !== 'no-cleanup') {
      if (CONFIG.verbose && outputMode === 'detailed') {
        console.log('\n===== 清理AI标记 =====');
      }

      const cleanupResult = cleanupAIMarkers();

      if (CONFIG.verbose) {
        if (outputMode === 'detailed') {
          if (cleanupResult.totalMarkersRemoved > 0) {
            console.log(`已清理 ${cleanupResult.cleanedFiles} 个文件中的 ${cleanupResult.totalMarkersRemoved} 个AI标记`);
          } else {
            console.log('没有发现需要清理的AI标记');
          }
        } else if (outputMode === 'summary' && cleanupResult.totalMarkersRemoved > 0) {
          console.log(`  清理: 移除了 ${cleanupResult.totalMarkersRemoved} 个AI标记`);
        }
      }
    }

  } catch (error) {
    console.error(`执行出错: ${error.message}`);
    process.exit(1);
  }
}

// 执行主函数
main();
