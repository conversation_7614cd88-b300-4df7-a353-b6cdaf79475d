#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');

/**
 * 清理工作目录中的AI标记
 */
function cleanupAIMarkers(verbose = true) {
  try {
    if (verbose) {
      console.log('开始清理AI标记...\n');
    }
    
    // 获取当前工作目录中的所有文件（排除.git目录）
    const findCommand = `find . -type f -not -path "./.git/*" -not -path "./node_modules/*" -not -name "*.log" -not -name "*.tmp"`;
    const allFiles = execSync(findCommand, { encoding: 'utf-8' })
      .trim()
      .split('\n')
      .filter(Boolean);
    
    let cleanedFiles = 0;
    let totalMarkersRemoved = 0;
    const processedFiles = [];
    
    allFiles.forEach(filePath => {
      try {
        // 跳过清理脚本自身，避免破坏文件结构
        if (filePath.includes('cleanup-markers.js') || filePath.endsWith('.md')) {
          return;
        }
        
        // 读取文件内容
        const content = fs.readFileSync(filePath, 'utf-8');
        
        // 检查是否包含AI标记
        if (content.includes('[[GBAI STAR]]') || content.includes('[[GBAI END]]')) {
          // 清理AI标记
          let cleanedContent = content;
          let markersInFile = 0;
          
          // 计算并移除标记
          const starMatches = content.match(/.*\[\[GBAI STAR\]\].*/g) || [];
          const endMatches = content.match(/.*\[\[GBAI END\]\].*/g) || [];
          markersInFile = starMatches.length + endMatches.length;
          
          // 移除包含标记的整行
          cleanedContent = cleanedContent
            .split('\n')
            .filter(line => !line.includes('[[GBAI STAR]]') && !line.includes('[[GBAI END]]'))
            .join('\n');
          
          // 写回文件
          fs.writeFileSync(filePath, cleanedContent, 'utf-8');
          
          if (markersInFile > 0) {
            cleanedFiles++;
            totalMarkersRemoved += markersInFile;
            processedFiles.push({ file: filePath, markers: markersInFile });
            
            if (verbose) {
              console.log(`✓ ${filePath} (移除 ${markersInFile} 个标记)`);
            }
          }
        }
      } catch (error) {
        // 忽略无法处理的文件（如二进制文件）
        if (verbose && !error.message.includes('EISDIR') && !error.message.includes('ENOENT')) {
          console.error(`⚠ 跳过文件 ${filePath}: ${error.message}`);
        }
      }
    });
    
    // 输出汇总信息
    if (verbose) {
      console.log('\n===== 清理完成 =====');
      if (totalMarkersRemoved > 0) {
        console.log(`✅ 成功清理了 ${cleanedFiles} 个文件中的 ${totalMarkersRemoved} 个AI标记`);
        
        if (processedFiles.length > 0) {
          console.log('\n处理的文件列表:');
          processedFiles.forEach(({ file, markers }) => {
            console.log(`  - ${file}: ${markers} 个标记`);
          });
        }
        
        console.log('\n💡 提示: 现在可以安全地提交代码，不会包含AI标记');
      } else {
        console.log('ℹ️  没有发现需要清理的AI标记');
      }
    } else {
      // 静默模式下的简洁输出
      if (totalMarkersRemoved > 0) {
        console.log(`清理了 ${cleanedFiles} 个文件中的 ${totalMarkersRemoved} 个AI标记`);
      }
    }
    
    return { cleanedFiles, totalMarkersRemoved, processedFiles };
  } catch (error) {
    console.error(`❌ 清理AI标记时出错: ${error.message}`);
    return { cleanedFiles: 0, totalMarkersRemoved: 0, processedFiles: [] };
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const verbose = !process.argv.includes('--quiet');
  const result = cleanupAIMarkers(verbose);
  
  // 设置退出码：0表示成功，1表示出错
  if (result.cleanedFiles >= 0) {
    process.exit(0);
  } else {
    process.exit(1);
  }
}

module.exports = { cleanupAIMarkers };
